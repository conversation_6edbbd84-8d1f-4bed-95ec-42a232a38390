package routes

import (
	"ai_select_admin/controllers"
	"ai_select_admin/middleware"

	"github.com/gin-gonic/gin"
)

var r *gin.Engine

func init() {
	r = gin.New()
}
func SetupRoutes() *gin.Engine {
	// 添加中间件
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RequestBodyLoggerMiddleware())
	r.Use(middleware.ErrorLoggerMiddleware())
	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建控制器实例
	adminController := &controllers.AdminController{}
	categoryController := &controllers.CategoryController{}
	videoController := &controllers.VideoController{}
	uploadController := controllers.NewUploadController()
	tencentAuthController := controllers.NewTencentAuthController()
	tencentNotifyController := controllers.NewTencentNotifyController()
	// API路由组
	api := r.Group("/api")
	{
		// 公开路由
		public := api.Group("/public")
		{
			public.POST("/admin/login", adminController.Login)
			public.GET("/upload/credentials", uploadController.GetUploadCredentials)
			public.GET("/upload/cos-url", uploadController.GetCOSURL)
		}

		// 腾讯云通知路由（无需认证）
		tencent := api.Group("/tencent")
		{
			tencent.POST("/videonotify", tencentNotifyController.VideoNotify)
		}

		// 需要认证的路由
		auth := api.Group("/auth")
		auth.Use(middleware.AuthMiddleware())
		{
			// 管理员个人信息
			auth.GET("/admin/profile", adminController.GetProfile)
			auth.GET("/get_upload_vedio_signature", tencentAuthController.GetSignature)
		}

		// 需要管理员权限的路由
		admin := api.Group("/admin")
		admin.Use(middleware.AdminAuthMiddleware())
		{
			// 管理员管理
			adminGroup := admin.Group("/admins")
			{
				adminGroup.GET("", adminController.ListAdmins)
				adminGroup.POST("", adminController.CreateAdmin)
				adminGroup.PUT("/:id", adminController.UpdateAdmin)
				adminGroup.DELETE("/:id", adminController.DeleteAdmin)
			}

			// 课程分类管理
			categoryGroup := admin.Group("/categories")
			{
				categoryGroup.GET("", categoryController.ListCategories)
				categoryGroup.GET("/tree", categoryController.GetCategoryTree)
				categoryGroup.GET("/:id", categoryController.GetCategory)
				categoryGroup.POST("", categoryController.CreateCategory)
				categoryGroup.PUT("/:id", categoryController.UpdateCategory)
				categoryGroup.DELETE("/:id", categoryController.DeleteCategory)
			}

			// 视频管理
			videoGroup := admin.Group("/videos")
			{
				videoGroup.GET("", videoController.ListVideos)
				videoGroup.GET("/:id", videoController.GetVideo)
				videoGroup.POST("", videoController.CreateVideo)
				videoGroup.PUT("/:id", videoController.UpdateVideo)
				videoGroup.DELETE("/:id", videoController.DeleteVideo)
			}
		}
	}

	// 静态文件服务
	r.Static("/uploads", "./uploads")

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "AI Select Admin API is running",
		})
	})

	return r
}
