package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"encoding/json"
	"fmt"
	"io"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TencentNotifyController struct{}

func NewTencentNotifyController() *TencentNotifyController {
	return &TencentNotifyController{}
}

// 腾讯云视频上传完成通知结构
type VideoUploadNotify struct {
	EventType string `json:"EventType"`
	FileId    string `json:"FileId"`
	MediaInfo struct {
		FileId      string `json:"FileId"`
		FileName    string `json:"FileName"`
		FileSize    int64  `json:"FileSize"`
		Duration    int    `json:"Duration"`
		VideoInfo   struct {
			Width  int `json:"Width"`
			Height int `json:"Height"`
		} `json:"VideoInfo"`
		MediaUrl string `json:"MediaUrl"`
		CoverUrl string `json:"CoverUrl"`
	} `json:"MediaInfo"`
}

// 腾讯云任务流处理完成通知结构
type ProcedureTaskNotify struct {
	EventType     string `json:"EventType"`
	FileId        string `json:"FileId"`
	TaskId        string `json:"TaskId"`
	Status        string `json:"Status"`
	ErrCode       int    `json:"ErrCode"`
	Message       string `json:"Message"`
	ProcedureName string `json:"ProcedureName"`
	MediaInfo     struct {
		FileId      string `json:"FileId"`
		FileName    string `json:"FileName"`
		FileSize    int64  `json:"FileSize"`
		Duration    int    `json:"Duration"`
		VideoInfo   struct {
			Width  int `json:"Width"`
			Height int `json:"Height"`
		} `json:"VideoInfo"`
		MediaUrl string `json:"MediaUrl"`
		CoverUrl string `json:"CoverUrl"`
	} `json:"MediaInfo"`
	TranscodeTask struct {
		Status    string `json:"Status"`
		ErrCode   int    `json:"ErrCode"`
		Message   string `json:"Message"`
		OutputSet []struct {
			FileId   string `json:"FileId"`
			Url      string `json:"Url"`
			Bitrate  int    `json:"Bitrate"`
			Height   int    `json:"Height"`
			Width    int    `json:"Width"`
			Size     int64  `json:"Size"`
			Duration int    `json:"Duration"`
		} `json:"OutputSet"`
	} `json:"TranscodeTask"`
	CoverBySnapshotTask struct {
		Status    string `json:"Status"`
		ErrCode   int    `json:"ErrCode"`
		Message   string `json:"Message"`
		OutputSet []struct {
			CoverUrl string `json:"CoverUrl"`
		} `json:"OutputSet"`
	} `json:"CoverBySnapshotTask"`
}

// VideoNotify 处理腾讯云视频通知
func (tnc *TencentNotifyController) VideoNotify(c *gin.Context) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logger.Errorf("读取通知请求体失败: %v", err)
		utils.BadRequest(c, "读取请求体失败")
		return
	}

	logger.Infof("收到腾讯云视频通知: %s", string(body))

	// 解析通知类型
	var baseNotify struct {
		EventType string `json:"EventType"`
		FileId    string `json:"FileId"`
	}

	if err := json.Unmarshal(body, &baseNotify); err != nil {
		logger.Errorf("解析通知数据失败: %v", err)
		utils.BadRequest(c, "解析通知数据失败")
		return
	}

	switch baseNotify.EventType {
	case "NewFileUpload":
		// 视频上传完成通知
		tnc.handleVideoUploadNotify(body)
	case "ProcedureStateChanged":
		// 任务流处理完成通知
		tnc.handleProcedureTaskNotify(body)
	default:
		logger.Warnf("未知的通知类型: %s", baseNotify.EventType)
	}

	// 返回成功响应
	utils.Success(c, gin.H{"message": "通知处理成功"})
}

// handleVideoUploadNotify 处理视频上传完成通知
func (tnc *TencentNotifyController) handleVideoUploadNotify(body []byte) {
	var notify VideoUploadNotify
	if err := json.Unmarshal(body, &notify); err != nil {
		logger.Errorf("解析视频上传通知失败: %v", err)
		return
	}

	logger.Infof("处理视频上传完成通知: FileId=%s", notify.FileId)

	// 查找对应的视频记录
	var video models.Video
	if err := database.DB.Where("file_id = ?", notify.FileId).First(&video).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warnf("未找到对应的视频记录: FileId=%s", notify.FileId)
		} else {
			logger.Errorf("查询视频记录失败: %v", err)
		}
		return
	}

	// 更新视频状态为转码中
	video.Status = 2 // 转码中
	if err := database.DB.Save(&video).Error; err != nil {
		logger.Errorf("更新视频状态失败: %v", err)
		return
	}

	logger.Infof("视频状态已更新为转码中: ID=%d, FileId=%s", video.ID, notify.FileId)
}

// handleProcedureTaskNotify 处理任务流完成通知
func (tnc *TencentNotifyController) handleProcedureTaskNotify(body []byte) {
	var notify ProcedureTaskNotify
	if err := json.Unmarshal(body, &notify); err != nil {
		logger.Errorf("解析任务流通知失败: %v", err)
		return
	}

	logger.Infof("处理任务流完成通知: FileId=%s, Status=%s", notify.FileId, notify.Status)

	// 查找对应的视频记录
	var video models.Video
	if err := database.DB.Where("file_id = ?", notify.FileId).First(&video).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warnf("未找到对应的视频记录: FileId=%s", notify.FileId)
		} else {
			logger.Errorf("查询视频记录失败: %v", err)
		}
		return
	}

	if notify.Status == "FINISH" && notify.ErrCode == 0 {
		// 任务流处理成功
		tnc.handleProcedureSuccess(&video, &notify)
	} else {
		// 任务流处理失败
		tnc.handleProcedureFailure(&video, &notify)
	}
}

// handleProcedureSuccess 处理任务流成功
func (tnc *TencentNotifyController) handleProcedureSuccess(video *models.Video, notify *ProcedureTaskNotify) {
	logger.Infof("任务流处理成功: VideoID=%d, FileId=%s", video.ID, notify.FileId)

	// 更新视频信息
	if len(notify.TranscodeTask.OutputSet) > 0 {
		output := notify.TranscodeTask.OutputSet[0] // 取第一个转码输出
		video.PlayUrl = output.Url
		video.Duration = output.Duration
		video.Width = output.Width
		video.Height = output.Height
		video.FileSize = output.Size
	}

	// 更新封面信息
	if len(notify.CoverBySnapshotTask.OutputSet) > 0 {
		coverOutput := notify.CoverBySnapshotTask.OutputSet[0]
		if video.CoverUrl == "" { // 如果用户没有上传自定义封面，使用自动生成的封面
			video.CoverUrl = coverOutput.CoverUrl
		}
	}

	// 更新状态为正常
	video.Status = 3

	if err := database.DB.Save(video).Error; err != nil {
		logger.Errorf("更新视频信息失败: %v", err)
		return
	}

	logger.Infof("视频信息更新成功: ID=%d, PlayUrl=%s", video.ID, video.PlayUrl)

	// TODO: 删除原始视频文件
	// tnc.deleteOriginalVideo(notify.FileId)
}

// handleProcedureFailure 处理任务流失败
func (tnc *TencentNotifyController) handleProcedureFailure(video *models.Video, notify *ProcedureTaskNotify) {
	logger.Errorf("任务流处理失败: VideoID=%d, FileId=%s, ErrCode=%d, Message=%s",
		video.ID, notify.FileId, notify.ErrCode, notify.Message)

	// 更新状态为失败
	video.Status = 4
	if err := database.DB.Save(video).Error; err != nil {
		logger.Errorf("更新视频状态失败: %v", err)
	}
}

// deleteOriginalVideo 删除原始视频文件
func (tnc *TencentNotifyController) deleteOriginalVideo(fileId string) {
	// TODO: 调用腾讯云VOD API删除原始视频文件
	logger.Infof("TODO: 删除原始视频文件: FileId=%s", fileId)
}
