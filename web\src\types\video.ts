import type { Category } from './category'

// 视频状态枚举
export enum VideoStatus {
  UPLOADING = 1,    // 上传中
  TRANSCODING = 2,  // 转码中
  NORMAL = 3,       // 正常
  FAILED = 4        // 失败
}

// 视频基本信息
export interface Video {
  id: number
  title: string
  description: string
  file_name: string
  file_id: string
  play_url: string
  cover_url: string
  duration: number
  file_size: number
  width: number
  height: number
  format: string
  status: VideoStatus
  status_text: string
  category_id: number
  category?: Category
  created_at: string
  updated_at: string
}

// 视频列表请求参数
export interface VideoListRequest {
  page?: number
  page_size?: number
  title?: string
  status?: VideoStatus
  category_id?: number
}

// 创建视频请求参数
export interface VideoCreateRequest {
  title: string
  description?: string
  file_name: string
  file_id: string
  play_url?: string
  cover_url?: string
  duration?: number
  file_size?: number
  width?: number
  height?: number
  format?: string
  category_id?: number
}

// 更新视频请求参数
export interface VideoUpdateRequest {
  title: string
  description?: string
  play_url?: string
  cover_url?: string
  duration?: number
  file_size?: number
  width?: number
  height?: number
  format?: string
  status?: VideoStatus
  category_id?: number
}

// 腾讯云上传签名响应
export interface UploadSignatureResponse {
  signature: string
}

// 腾讯云上传结果
export interface UploadResult {
  fileId: string
  mediaUrl: string
  coverUrl?: string
  size: number
  duration: number
  width: number
  height: number
}

// 上传进度信息
export interface UploadProgress {
  loaded: number
  total: number
  percent: number
}

// 视频上传状态
export interface VideoUploadStatus {
  file: File
  title: string
  cover_url: string
  category_id: number
  progress: UploadProgress
  status: 'waiting' | 'uploading' | 'success' | 'error'
  result?: UploadResult
  error?: string
}
