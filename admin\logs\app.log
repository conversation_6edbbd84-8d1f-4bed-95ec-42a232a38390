{"level":"info","msg":"日志系统初始化完成","time":"2025-06-27 15:02:56"}
{"body_size":45,"client_ip":"::1","error":"","latency":51358500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 15:28:34","timestamp":"2025-06-27 15:28:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":111505900,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 15:28:53","timestamp":"2025-06-27 15:28:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":50983500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 15:29:04","timestamp":"2025-06-27 15:29:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin01","time":"2025-06-27 15:33:09"}
{"body_size":546,"client_ip":"::1","error":"","latency":327393400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 15:33:09","timestamp":"2025-06-27 15:33:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":236115400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:41:49","timestamp":"2025-06-27 15:41:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":63526600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:41:52","timestamp":"2025-06-27 15:41:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":47068200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:41:53","timestamp":"2025-06-27 15:41:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":54841900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:42:05","timestamp":"2025-06-27 15:42:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":111847200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:43:00","timestamp":"2025-06-27 15:43:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":65069700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:47:36","timestamp":"2025-06-27 15:47:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":45284300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:48:42","timestamp":"2025-06-27 15:48:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":51027600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:48:46","timestamp":"2025-06-27 15:48:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":57514400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:48:49","timestamp":"2025-06-27 15:48:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":47659500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:49:00","timestamp":"2025-06-27 15:49:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":63128900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:51:56","timestamp":"2025-06-27 15:51:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":56617400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:51:59","timestamp":"2025-06-27 15:51:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":53025500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:53:00","timestamp":"2025-06-27 15:53:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":45369200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:53:28","timestamp":"2025-06-27 15:53:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":78127800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:53:47","timestamp":"2025-06-27 15:53:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":51296200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:54:12","timestamp":"2025-06-27 15:54:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":176588200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:54:57","timestamp":"2025-06-27 15:54:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":72818000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:55:05","timestamp":"2025-06-27 15:55:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":60000500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:55:07","timestamp":"2025-06-27 15:55:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":79401400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:55:08","timestamp":"2025-06-27 15:55:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":87865200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:55:18","timestamp":"2025-06-27 15:55:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":158755000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:55:47","timestamp":"2025-06-27 15:55:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":102823000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:55:57","timestamp":"2025-06-27 15:55:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":182342000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:56:14","timestamp":"2025-06-27 15:56:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":106874300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:56:16","timestamp":"2025-06-27 15:56:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":83499600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:56:17","timestamp":"2025-06-27 15:56:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":51949100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 15:56:34","timestamp":"2025-06-27 15:56:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":46408100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:56:51","timestamp":"2025-06-27 15:56:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":71016000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 15:59:21","timestamp":"2025-06-27 15:59:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":107985100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:03:31","timestamp":"2025-06-27 16:03:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":45882000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:03:32","timestamp":"2025-06-27 16:03:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":51998800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:03:39","timestamp":"2025-06-27 16:03:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":45581800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:03:39","timestamp":"2025-06-27 16:03:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":86317500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:04:56","timestamp":"2025-06-27 16:04:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":54239800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:06:42","timestamp":"2025-06-27 16:06:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":52365400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:06:43","timestamp":"2025-06-27 16:06:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":65136200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:06:46","timestamp":"2025-06-27 16:06:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":327375300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:06:48","timestamp":"2025-06-27 16:06:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":349292600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:06:50","timestamp":"2025-06-27 16:06:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":85888700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:06:51","timestamp":"2025-06-27 16:06:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":78094700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:08:08","timestamp":"2025-06-27 16:08:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":61038300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:08:10","timestamp":"2025-06-27 16:08:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":46150800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:08:34","timestamp":"2025-06-27 16:08:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":49886200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:08:48","timestamp":"2025-06-27 16:08:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":135489000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:12:03","timestamp":"2025-06-27 16:12:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":222898500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:14:57","timestamp":"2025-06-27 16:14:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":70449400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:17:27","timestamp":"2025-06-27 16:17:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":71454800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:17:43","timestamp":"2025-06-27 16:17:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":64449200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:19:21","timestamp":"2025-06-27 16:19:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":113295400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:19:33","timestamp":"2025-06-27 16:19:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":99709400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:22:27","timestamp":"2025-06-27 16:22:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":837440900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:22:48","timestamp":"2025-06-27 16:22:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":83203200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:23:01","timestamp":"2025-06-27 16:23:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":76846100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:23:12","timestamp":"2025-06-27 16:23:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":432817100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:23:51","timestamp":"2025-06-27 16:23:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":71057200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:24:04","timestamp":"2025-06-27 16:24:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":168109200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=20\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:24:16","timestamp":"2025-06-27 16:24:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":557,"client_ip":"::1","error":"","latency":228215700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:26:47","timestamp":"2025-06-27 16:26:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-06-27 16:29:52"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:30:35"}
{"body_size":658,"client_ip":"::1","error":"","latency":187563100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=20\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:30:35","timestamp":"2025-06-27 16:30:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751013059099456700.png","time":"2025-06-27 16:30:59"}
{"body_size":137,"client_ip":"::1","error":"","latency":40682300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 16:30:59","timestamp":"2025-06-27 16:30:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751013078314183200.png","time":"2025-06-27 16:31:18"}
{"body_size":137,"client_ip":"::1","error":"","latency":24666600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 16:31:18","timestamp":"2025-06-27 16:31:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":130156500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:33:25","timestamp":"2025-06-27 16:33:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:33:34"}
{"body_size":658,"client_ip":"::1","error":"","latency":84215300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:33:34","timestamp":"2025-06-27 16:33:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751013249414479000.png","time":"2025-06-27 16:34:09"}
{"body_size":137,"client_ip":"::1","error":"","latency":14999700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 16:34:09","timestamp":"2025-06-27 16:34:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:37:32"}
{"body_size":658,"client_ip":"::1","error":"","latency":61636800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:37:32","timestamp":"2025-06-27 16:37:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:37:34"}
{"body_size":658,"client_ip":"::1","error":"","latency":57128200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:37:34","timestamp":"2025-06-27 16:37:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:09"}
{"body_size":658,"client_ip":"::1","error":"","latency":58062800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:09","timestamp":"2025-06-27 16:38:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:09"}
{"body_size":658,"client_ip":"::1","error":"","latency":527902800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:09","timestamp":"2025-06-27 16:38:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:26"}
{"body_size":658,"client_ip":"::1","error":"","latency":762677500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:26","timestamp":"2025-06-27 16:38:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:27"}
{"body_size":658,"client_ip":"::1","error":"","latency":113776500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:27","timestamp":"2025-06-27 16:38:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:47"}
{"body_size":658,"client_ip":"::1","error":"","latency":46779600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:47","timestamp":"2025-06-27 16:38:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:38:48"}
{"body_size":658,"client_ip":"::1","error":"","latency":47532300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:38:48","timestamp":"2025-06-27 16:38:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:39:14"}
{"body_size":658,"client_ip":"::1","error":"","latency":68323600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:39:14","timestamp":"2025-06-27 16:39:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:39:19"}
{"body_size":658,"client_ip":"::1","error":"","latency":51553100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:39:19","timestamp":"2025-06-27 16:39:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:40:15"}
{"body_size":658,"client_ip":"::1","error":"","latency":47245200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:40:15","timestamp":"2025-06-27 16:40:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:40:15"}
{"body_size":658,"client_ip":"::1","error":"","latency":49806200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:40:15","timestamp":"2025-06-27 16:40:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:40:35"}
{"body_size":658,"client_ip":"::1","error":"","latency":86280600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:40:35","timestamp":"2025-06-27 16:40:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=1, GeneratedReportTimes=0, AllReportTimes=0","time":"2025-06-27 16:40:36"}
{"body_size":658,"client_ip":"::1","error":"","latency":48062600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:40:36","timestamp":"2025-06-27 16:40:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751013779754615600.png","time":"2025-06-27 16:42:59"}
{"body_size":137,"client_ip":"::1","error":"","latency":51789800,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 16:42:59","timestamp":"2025-06-27 16:42:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":223294000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 16:43:00","timestamp":"2025-06-27 16:43:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":126,"client_ip":"::1","error":"","latency":0,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":200,"time":"2025-06-27 16:43:01","timestamp":"2025-06-27 16:43:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":126,"client_ip":"::1","error":"","latency":0,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":200,"time":"2025-06-27 16:43:03","timestamp":"2025-06-27 16:43:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":126,"client_ip":"::1","error":"","latency":0,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":200,"time":"2025-06-27 16:43:09","timestamp":"2025-06-27 16:43:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"更新用户成功: user001","time":"2025-06-27 16:43:32"}
{"body_size":397,"client_ip":"::1","error":"","latency":262895500,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":200,"time":"2025-06-27 16:43:32","timestamp":"2025-06-27 16:43:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:43:32"}
{"body_size":741,"client_ip":"::1","error":"","latency":115714500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:43:32","timestamp":"2025-06-27 16:43:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":7012200,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 16:43:32","timestamp":"2025-06-27 16:43:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:45:23"}
{"body_size":741,"client_ip":"::1","error":"","latency":49922800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:45:23","timestamp":"2025-06-27 16:45:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:45:23"}
{"body_size":741,"client_ip":"::1","error":"","latency":49459600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:45:23","timestamp":"2025-06-27 16:45:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:45:24","timestamp":"2025-06-27 16:45:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:45:54"}
{"body_size":741,"client_ip":"::1","error":"","latency":69003700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:45:54","timestamp":"2025-06-27 16:45:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:45:54"}
{"body_size":741,"client_ip":"::1","error":"","latency":48715300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:45:54","timestamp":"2025-06-27 16:45:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:46:16"}
{"body_size":741,"client_ip":"::1","error":"","latency":48631500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:46:16","timestamp":"2025-06-27 16:46:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:46:16"}
{"body_size":741,"client_ip":"::1","error":"","latency":52219600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:46:16","timestamp":"2025-06-27 16:46:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:47:08"}
{"body_size":741,"client_ip":"::1","error":"","latency":82331900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:47:08","timestamp":"2025-06-27 16:47:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:47:08","timestamp":"2025-06-27 16:47:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":81401700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:47:39","timestamp":"2025-06-27 16:47:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:47:53"}
{"body_size":741,"client_ip":"::1","error":"","latency":49852000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:47:53","timestamp":"2025-06-27 16:47:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:48:29"}
{"body_size":741,"client_ip":"::1","error":"","latency":53432300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:48:29","timestamp":"2025-06-27 16:48:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:48:59"}
{"body_size":741,"client_ip":"::1","error":"","latency":53238200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:48:59","timestamp":"2025-06-27 16:48:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:49:08"}
{"body_size":741,"client_ip":"::1","error":"","latency":105241200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:49:08","timestamp":"2025-06-27 16:49:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":112971000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 16:50:17","timestamp":"2025-06-27 16:50:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:50:21"}
{"body_size":741,"client_ip":"::1","error":"","latency":57453300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:50:21","timestamp":"2025-06-27 16:50:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:50:21","timestamp":"2025-06-27 16:50:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:52:41"}
{"body_size":741,"client_ip":"::1","error":"","latency":61411600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:52:41","timestamp":"2025-06-27 16:52:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:52:45"}
{"body_size":741,"client_ip":"::1","error":"","latency":212043000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:52:45","timestamp":"2025-06-27 16:52:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":4997000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:52:45","timestamp":"2025-06-27 16:52:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:53:05"}
{"body_size":741,"client_ip":"::1","error":"","latency":46706300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:53:05","timestamp":"2025-06-27 16:53:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:53:05","timestamp":"2025-06-27 16:53:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:53:50"}
{"body_size":741,"client_ip":"::1","error":"","latency":208183800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:53:50","timestamp":"2025-06-27 16:53:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:53:51"}
{"body_size":741,"client_ip":"::1","error":"","latency":50021200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:53:51","timestamp":"2025-06-27 16:53:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:54:07"}
{"body_size":741,"client_ip":"::1","error":"","latency":50587300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:54:07","timestamp":"2025-06-27 16:54:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:54:07"}
{"body_size":741,"client_ip":"::1","error":"","latency":48113300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:54:07","timestamp":"2025-06-27 16:54:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:55:11"}
{"body_size":741,"client_ip":"::1","error":"","latency":46601400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:55:11","timestamp":"2025-06-27 16:55:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:55:11"}
{"body_size":741,"client_ip":"::1","error":"","latency":58874400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:55:11","timestamp":"2025-06-27 16:55:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:56:10"}
{"body_size":741,"client_ip":"::1","error":"","latency":51385600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:56:10","timestamp":"2025-06-27 16:56:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:56:10"}
{"body_size":741,"client_ip":"::1","error":"","latency":54615100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:56:10","timestamp":"2025-06-27 16:56:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:56:15"}
{"body_size":741,"client_ip":"::1","error":"","latency":48497200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:56:15","timestamp":"2025-06-27 16:56:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:56:20"}
{"body_size":741,"client_ip":"::1","error":"","latency":51996100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:56:20","timestamp":"2025-06-27 16:56:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:59:34"}
{"body_size":741,"client_ip":"::1","error":"","latency":70434800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:59:34","timestamp":"2025-06-27 16:59:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 16:59:34","timestamp":"2025-06-27 16:59:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:59:34"}
{"body_size":741,"client_ip":"::1","error":"","latency":51806300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:59:34","timestamp":"2025-06-27 16:59:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 16:59:39"}
{"body_size":741,"client_ip":"::1","error":"","latency":98677400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 16:59:39","timestamp":"2025-06-27 16:59:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:01:34"}
{"body_size":741,"client_ip":"::1","error":"","latency":219033500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:01:34","timestamp":"2025-06-27 17:01:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":1000000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:01:34","timestamp":"2025-06-27 17:01:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:04:42"}
{"body_size":741,"client_ip":"::1","error":"","latency":208280200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:04:42","timestamp":"2025-06-27 17:04:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":997600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:04:42","timestamp":"2025-06-27 17:04:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:09:53"}
{"body_size":741,"client_ip":"::1","error":"","latency":125251700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:09:53","timestamp":"2025-06-27 17:09:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":527800,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:09:54","timestamp":"2025-06-27 17:09:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":157473500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 17:15:02","timestamp":"2025-06-27 17:15:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin01","time":"2025-06-27 17:15:30"}
{"body_size":546,"client_ip":"::1","error":"","latency":170516400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 17:15:30","timestamp":"2025-06-27 17:15:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":507281500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 17:15:33","timestamp":"2025-06-27 17:15:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:15:37"}
{"body_size":741,"client_ip":"::1","error":"","latency":129126900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:15:38","timestamp":"2025-06-27 17:15:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":1003600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:15:38","timestamp":"2025-06-27 17:15:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除用户成功: ID=1","time":"2025-06-27 17:15:42"}
{"body_size":33,"client_ip":"::1","error":"","latency":159050400,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/users/1","status_code":200,"time":"2025-06-27 17:15:42","timestamp":"2025-06-27 17:15:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:15:42"}
{"body_size":445,"client_ip":"::1","error":"","latency":52645500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:15:42","timestamp":"2025-06-27 17:15:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":77004200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 17:16:10","timestamp":"2025-06-27 17:16:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:17:53"}
{"body_size":445,"client_ip":"::1","error":"","latency":145680800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:17:53","timestamp":"2025-06-27 17:17:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:23:11"}
{"body_size":445,"client_ip":"::1","error":"","latency":148956600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:23:11","timestamp":"2025-06-27 17:23:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:23:11","timestamp":"2025-06-27 17:23:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":127922300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 17:23:13","timestamp":"2025-06-27 17:23:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:23:20"}
{"body_size":445,"client_ip":"::1","error":"","latency":148436600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:23:20","timestamp":"2025-06-27 17:23:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751016247118560500.jpg","time":"2025-06-27 17:24:07"}
{"body_size":137,"client_ip":"::1","error":"","latency":14718800,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 17:24:07","timestamp":"2025-06-27 17:24:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":1006100,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016247118560500.jpg","status_code":200,"time":"2025-06-27 17:24:07","timestamp":"2025-06-27 17:24:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":123,"client_ip":"::1","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users","status_code":200,"time":"2025-06-27 17:24:08","timestamp":"2025-06-27 17:24:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":123,"client_ip":"::1","error":"","latency":1001100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users","status_code":200,"time":"2025-06-27 17:24:23","timestamp":"2025-06-27 17:24:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=2, GeneratedReportTimes=377, AllReportTimes=1000","time":"2025-06-27 17:26:49"}
{"body_size":445,"client_ip":"::1","error":"","latency":92252700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:26:49","timestamp":"2025-06-27 17:26:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"头像上传成功: uploads\\avatars\\avatar_1751016439860023300.jpg","time":"2025-06-27 17:27:19"}
{"body_size":137,"client_ip":"::1","error":"","latency":124713000,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/upload/avatar","status_code":200,"time":"2025-06-27 17:27:19","timestamp":"2025-06-27 17:27:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":1001000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 17:27:19","timestamp":"2025-06-27 17:27:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":126,"client_ip":"::1","error":"","latency":514200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users","status_code":200,"time":"2025-06-27 17:27:21","timestamp":"2025-06-27 17:27:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建用户成功: user002","time":"2025-06-27 17:27:35"}
{"body_size":378,"client_ip":"::1","error":"","latency":333234600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users","status_code":200,"time":"2025-06-27 17:27:35","timestamp":"2025-06-27 17:27:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:27:35"}
{"body_size":783,"client_ip":"::1","error":"","latency":51391400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:27:35","timestamp":"2025-06-27 17:27:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:29:38"}
{"body_size":783,"client_ip":"::1","error":"","latency":127083500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:29:38","timestamp":"2025-06-27 17:29:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":21999600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":304,"time":"2025-06-27 17:29:38","timestamp":"2025-06-27 17:29:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":999700,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:29:38","timestamp":"2025-06-27 17:29:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"重置用户密码成功: user001","time":"2025-06-27 17:29:55"}
{"body_size":39,"client_ip":"::1","error":"","latency":170968100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users/2/reset-password","status_code":200,"time":"2025-06-27 17:29:55","timestamp":"2025-06-27 17:29:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:46:56"}
{"body_size":783,"client_ip":"::1","error":"","latency":65454600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:46:56","timestamp":"2025-06-27 17:46:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:46:57"}
{"body_size":783,"client_ip":"::1","error":"","latency":47594100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:46:57","timestamp":"2025-06-27 17:46:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":1000600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:46:57","timestamp":"2025-06-27 17:46:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":304,"time":"2025-06-27 17:46:57","timestamp":"2025-06-27 17:46:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:47:25"}
{"body_size":783,"client_ip":"::1","error":"","latency":65271600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:47:25","timestamp":"2025-06-27 17:47:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:47:26"}
{"body_size":783,"client_ip":"::1","error":"","latency":65060000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:47:26","timestamp":"2025-06-27 17:47:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:54:48"}
{"body_size":783,"client_ip":"::1","error":"","latency":136079800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:54:48","timestamp":"2025-06-27 17:54:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":513100,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":304,"time":"2025-06-27 17:54:48","timestamp":"2025-06-27 17:54:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":9000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":304,"time":"2025-06-27 17:54:48","timestamp":"2025-06-27 17:54:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/upload/credentials?filename=108.png","status_code":404,"time":"2025-06-27 17:55:03","timestamp":"2025-06-27 17:55:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/upload/credentials?filename=1111111.png","status_code":404,"time":"2025-06-27 17:55:23","timestamp":"2025-06-27 17:55:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:57:59"}
{"body_size":783,"client_ip":"::1","error":"","latency":47777300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:57:59","timestamp":"2025-06-27 17:57:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":20548800,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 17:57:59","timestamp":"2025-06-27 17:57:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":4999100,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 17:57:59","timestamp":"2025-06-27 17:57:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 17:58:13"}
{"body_size":783,"client_ip":"::1","error":"","latency":55842400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 17:58:13","timestamp":"2025-06-27 17:58:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":1002100,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 17:58:13","timestamp":"2025-06-27 17:58:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":1998300,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 17:58:13","timestamp":"2025-06-27 17:58:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":404,"time":"2025-06-27 17:59:06","timestamp":"2025-06-27 17:59:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-06-27 17:59:25"}
{"level":"info","msg":"获取上传凭证成功: 1111111.png","time":"2025-06-27 17:59:38"}
{"body_size":1099,"client_ip":"::1","error":"","latency":203269300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":200,"time":"2025-06-27 17:59:38","timestamp":"2025-06-27 17:59:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"获取上传凭证成功: 1111111.png","time":"2025-06-27 17:59:51"}
{"body_size":1099,"client_ip":"::1","error":"","latency":44888900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":200,"time":"2025-06-27 17:59:51","timestamp":"2025-06-27 17:59:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:00:52"}
{"body_size":783,"client_ip":"::1","error":"","latency":368622900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:00:52","timestamp":"2025-06-27 18:00:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":116612300,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 18:00:52","timestamp":"2025-06-27 18:00:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":114610500,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:00:52","timestamp":"2025-06-27 18:00:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:01:05"}
{"body_size":783,"client_ip":"::1","error":"","latency":52469600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:01:05","timestamp":"2025-06-27 18:01:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":3520600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 18:01:05","timestamp":"2025-06-27 18:01:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":1530600,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:01:05","timestamp":"2025-06-27 18:01:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-06-27 18:01:59"}
{"level":"info","msg":"获取上传凭证成功: 1111111.png","time":"2025-06-27 18:02:11"}
{"body_size":1099,"client_ip":"::1","error":"","latency":267392800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":200,"time":"2025-06-27 18:02:11","timestamp":"2025-06-27 18:02:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":0,"client_ip":"::1","error":"","latency":196915100,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":500,"time":"2025-06-27 18:02:15","timestamp":"2025-06-27 18:02:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":0,"client_ip":"::1","error":"","latency":122096900,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":500,"time":"2025-06-27 18:02:23","timestamp":"2025-06-27 18:02:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"获取上传凭证成功: 1111111.png","time":"2025-06-27 18:03:10"}
{"body_size":1099,"client_ip":"::1","error":"","latency":53906500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":200,"time":"2025-06-27 18:03:10","timestamp":"2025-06-27 18:03:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:04:08"}
{"body_size":783,"client_ip":"::1","error":"","latency":383463400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:04:08","timestamp":"2025-06-27 18:04:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":111119500,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 18:04:09","timestamp":"2025-06-27 18:04:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":92443500,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:04:09","timestamp":"2025-06-27 18:04:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:04:42"}
{"body_size":783,"client_ip":"::1","error":"","latency":51656900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:04:42","timestamp":"2025-06-27 18:04:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"获取上传凭证成功: 1111111.png","time":"2025-06-27 18:05:18"}
{"body_size":1099,"client_ip":"::1","error":"","latency":109952100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=1111111.png","status_code":200,"time":"2025-06-27 18:05:18","timestamp":"2025-06-27 18:05:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":0,"client_ip":"::1","error":"","latency":134710500,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":500,"time":"2025-06-27 18:05:21","timestamp":"2025-06-27 18:05:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":0,"client_ip":"::1","error":"","latency":113386500,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":500,"time":"2025-06-27 18:07:03","timestamp":"2025-06-27 18:07:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-06-27 18:07:21"}
{"level":"info","msg":"更新用户成功: user001","time":"2025-06-27 18:07:25"}
{"body_size":437,"client_ip":"::1","error":"","latency":246581300,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/2","status_code":200,"time":"2025-06-27 18:07:25","timestamp":"2025-06-27 18:07:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:07:25"}
{"body_size":823,"client_ip":"::1","error":"","latency":246213800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:07:25","timestamp":"2025-06-27 18:07:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":378566000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:07:26","timestamp":"2025-06-27 18:07:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":999000,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 18:08:32","timestamp":"2025-06-27 18:08:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":2113500,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:08:34","timestamp":"2025-06-27 18:08:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"重置用户密码成功: user002","time":"2025-06-27 18:08:57"}
{"body_size":39,"client_ip":"::1","error":"","latency":197331200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/users/3/reset-password","status_code":200,"time":"2025-06-27 18:08:57","timestamp":"2025-06-27 18:08:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":381150,"client_ip":"::1","error":"","latency":15001500,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751016439860023300.jpg","status_code":200,"time":"2025-06-27 18:08:58","timestamp":"2025-06-27 18:08:58","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"获取上传凭证成功: 微信图片_20230919133616.jpg","time":"2025-06-27 18:09:06"}
{"body_size":1099,"client_ip":"::1","error":"","latency":316117000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20230919133616.jpg","status_code":200,"time":"2025-06-27 18:09:06","timestamp":"2025-06-27 18:09:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"更新用户成功: user002","time":"2025-06-27 18:09:08"}
{"body_size":418,"client_ip":"::1","error":"","latency":206468400,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/users/3","status_code":200,"time":"2025-06-27 18:09:08","timestamp":"2025-06-27 18:09:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":273452,"client_ip":"::1","error":"","latency":4071100,"level":"info","method":"GET","msg":"HTTP Request","path":"/uploads/avatars/avatar_1751013779754615600.png","status_code":200,"time":"2025-06-27 18:09:08","timestamp":"2025-06-27 18:09:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:09:09"}
{"body_size":863,"client_ip":"::1","error":"","latency":235923700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:09:09","timestamp":"2025-06-27 18:09:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":508,"client_ip":"::1","error":"","latency":105943800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:09:31","timestamp":"2025-06-27 18:09:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"获取上传凭证成功: 108.png","time":"2025-06-27 18:09:39"}
{"body_size":1099,"client_ip":"::1","error":"","latency":38609000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/public/upload/credentials?filename=108.png","status_code":200,"time":"2025-06-27 18:09:39","timestamp":"2025-06-27 18:09:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"更新管理员成功: admin01","time":"2025-06-27 18:09:41"}
{"body_size":356,"client_ip":"::1","error":"","latency":212606000,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/admins/2","status_code":200,"time":"2025-06-27 18:09:41","timestamp":"2025-06-27 18:09:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":595,"client_ip":"::1","error":"","latency":97496700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:09:41","timestamp":"2025-06-27 18:09:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":56521900,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 18:09:56","timestamp":"2025-06-27 18:09:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin01","time":"2025-06-27 18:10:04"}
{"body_size":633,"client_ip":"::1","error":"","latency":402906800,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-06-27 18:10:04","timestamp":"2025-06-27 18:10:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":595,"client_ip":"::1","error":"","latency":65490800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:10:04","timestamp":"2025-06-27 18:10:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:10:09"}
{"body_size":863,"client_ip":"::1","error":"","latency":63660800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:10:09","timestamp":"2025-06-27 18:10:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":595,"client_ip":"::1","error":"","latency":379907200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:13:43","timestamp":"2025-06-27 18:13:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:13:44"}
{"body_size":863,"client_ip":"::1","error":"","latency":190640400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:13:44","timestamp":"2025-06-27 18:13:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:17:28"}
{"body_size":863,"client_ip":"::1","error":"","latency":48430700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:17:28","timestamp":"2025-06-27 18:17:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"用户数据示例: ID=3, GeneratedReportTimes=0, AllReportTimes=1000","time":"2025-06-27 18:17:42"}
{"body_size":863,"client_ip":"::1","error":"","latency":75164200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/users?page=1\u0026size=10\u0026keyword=\u0026status=","status_code":200,"time":"2025-06-27 18:17:42","timestamp":"2025-06-27 18:17:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":595,"client_ip":"::1","error":"","latency":58493000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:19:02","timestamp":"2025-06-27 18:19:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":595,"client_ip":"::1","error":"","latency":145278200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-06-27 18:20:15","timestamp":"2025-06-27 18:20:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:15:14"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:23:51"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:24:10"}
{"body_size":82,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":401,"time":"2025-07-04 18:26:27","timestamp":"2025-07-04 18:26:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":42198500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:29:01","timestamp":"2025-07-04 18:29:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":14960300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:29:05","timestamp":"2025-07-04 18:29:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:30:10"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:33:32"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:35:37"}
{"body_size":45,"client_ip":"::1","error":"","latency":1585200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:35:59","timestamp":"2025-07-04 18:35:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":32499500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:36:12","timestamp":"2025-07-04 18:36:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:38:51"}
{"body_size":45,"client_ip":"::1","error":"","latency":48014000,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:38:56","timestamp":"2025-07-04 18:38:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:40:07"}
{"body_size":45,"client_ip":"::1","error":"","latency":4171700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:40:10","timestamp":"2025-07-04 18:40:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询管理员失败: invalid connection","time":"2025-07-04 18:43:01"}
{"body_size":33,"client_ip":"::1","error":"","latency":5532300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:43:01","timestamp":"2025-07-04 18:43:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:43:08"}
{"body_size":45,"client_ip":"::1","error":"","latency":3994100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:43:11","timestamp":"2025-07-04 18:43:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":120769400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:43:14","timestamp":"2025-07-04 18:43:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:43:31"}
{"body_size":45,"client_ip":"::1","error":"","latency":9636500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:43:34","timestamp":"2025-07-04 18:43:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":31648400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:44:14","timestamp":"2025-07-04 18:44:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":45,"client_ip":"::1","error":"","latency":2290000,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:44:20","timestamp":"2025-07-04 18:44:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:44:42"}
{"body_size":45,"client_ip":"::1","error":"","latency":2387100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:44:45","timestamp":"2025-07-04 18:44:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:48:08"}
{"body_size":45,"client_ip":"::1","error":"","latency":43440200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:48:21","timestamp":"2025-07-04 18:48:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:48:49"}
{"body_size":45,"client_ip":"::1","error":"","latency":45934400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:48:53","timestamp":"2025-07-04 18:48:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin","time":"2025-07-04 18:49:10"}
{"body_size":533,"client_ip":"::1","error":"","latency":200514600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-04 18:49:10","timestamp":"2025-07-04 18:49:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 18:49:19"}
{"body_size":316,"client_ip":"::1","error":"","latency":65349600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-04 18:49:46","timestamp":"2025-07-04 18:49:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-04 19:02:50"}
{"body_size":316,"client_ip":"::1","error":"","latency":3544600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-04 19:02:55","timestamp":"2025-07-04 19:02:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":12634800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?name=\u0026page=1\u0026page_size=10","status_code":200,"time":"2025-07-04 19:02:57","timestamp":"2025-07-04 19:02:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-04 19:03:02","timestamp":"2025-07-04 19:03:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-04 19:03:06","timestamp":"2025-07-04 19:03:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-04 19:03:24","timestamp":"2025-07-04 19:03:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-04 19:05:25","timestamp":"2025-07-04 19:05:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-04 19:05:29","timestamp":"2025-07-04 19:05:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":5002200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?name=\u0026page=1\u0026page_size=10","status_code":200,"time":"2025-07-04 19:09:22","timestamp":"2025-07-04 19:09:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":27546400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-04 19:09:41","timestamp":"2025-07-04 19:09:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:06:38"}
{"body_size":316,"client_ip":"::1","error":"","latency":15359800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:07:05","timestamp":"2025-07-05 14:07:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":65429500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:07:17","timestamp":"2025-07-05 14:07:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":65760400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:08:59","timestamp":"2025-07-05 14:08:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:11:46"}
{"body_size":316,"client_ip":"::1","error":"","latency":13010700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:13:11","timestamp":"2025-07-05 14:13:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":7743400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:13:54","timestamp":"2025-07-05 14:13:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:14:22"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:14:50"}
{"body_size":316,"client_ip":"::1","error":"","latency":6476200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:19:36","timestamp":"2025-07-05 14:19:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":54726600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:20:52","timestamp":"2025-07-05 14:20:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":1818700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:21:22","timestamp":"2025-07-05 14:21:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":33983400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:21:49","timestamp":"2025-07-05 14:21:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":1709300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:21:54","timestamp":"2025-07-05 14:21:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建分类成功: 测试","time":"2025-07-05 14:22:07"}
{"body_size":208,"client_ip":"::1","error":"","latency":21786400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/categories","status_code":200,"time":"2025-07-05 14:22:07","timestamp":"2025-07-05 14:22:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":256,"client_ip":"::1","error":"","latency":21317800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:22:08","timestamp":"2025-07-05 14:22:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":256,"client_ip":"::1","error":"","latency":23008100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:22:23","timestamp":"2025-07-05 14:22:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":256,"client_ip":"::1","error":"","latency":33391000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:22:24","timestamp":"2025-07-05 14:22:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":256,"client_ip":"::1","error":"","latency":3688500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:22:26","timestamp":"2025-07-05 14:22:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:23:27"}
{"body_size":91,"client_ip":"::1","error":"","latency":70354200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:23:33","timestamp":"2025-07-05 14:23:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建分类成功: 测试","time":"2025-07-05 14:23:40"}
{"body_size":209,"client_ip":"::1","error":"","latency":41680000,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/categories","status_code":200,"time":"2025-07-05 14:23:40","timestamp":"2025-07-05 14:23:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":16816000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:23:40","timestamp":"2025-07-05 14:23:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取分类总数失败: invalid connection","time":"2025-07-05 14:26:36"}
{"body_size":33,"client_ip":"::1","error":"","latency":2233400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:26:36","timestamp":"2025-07-05 14:26:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":4409200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:26:54","timestamp":"2025-07-05 14:26:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:29:48"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-05 14:33:28","timestamp":"2025-07-05 14:33:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":46789500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:33:28","timestamp":"2025-07-05 14:33:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":12588200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 14:33:42","timestamp":"2025-07-05 14:33:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-05 14:33:44","timestamp":"2025-07-05 14:33:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":12006300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:33:44","timestamp":"2025-07-05 14:33:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":2114800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:33:56","timestamp":"2025-07-05 14:33:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-05 14:33:57","timestamp":"2025-07-05 14:33:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":132,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=1000\u0026status=1","status_code":200,"time":"2025-07-05 14:34:22","timestamp":"2025-07-05 14:34:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":4585600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:34:53","timestamp":"2025-07-05 14:34:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":258,"client_ip":"::1","error":"","latency":5140500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-05 14:34:53","timestamp":"2025-07-05 14:34:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":258,"client_ip":"::1","error":"","latency":150501300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-05 14:35:16","timestamp":"2025-07-05 14:35:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":335118000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:35:16","timestamp":"2025-07-05 14:35:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":6421000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:35:54","timestamp":"2025-07-05 14:35:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":258,"client_ip":"::1","error":"","latency":12501700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-05 14:35:54","timestamp":"2025-07-05 14:35:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":2110300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:36:03","timestamp":"2025-07-05 14:36:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":9046400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:36:03","timestamp":"2025-07-05 14:36:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":51036000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:40:39","timestamp":"2025-07-05 14:40:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":41443600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:40:43","timestamp":"2025-07-05 14:40:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取分类总数失败: invalid connection","time":"2025-07-05 14:43:04"}
{"body_size":33,"client_ip":"::1","error":"","latency":1766300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:04","timestamp":"2025-07-05 14:43:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:43:21"}
{"body_size":257,"client_ip":"::1","error":"","latency":1636700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:24","timestamp":"2025-07-05 14:43:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":3001300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:26","timestamp":"2025-07-05 14:43:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":5057200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:28","timestamp":"2025-07-05 14:43:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":3920900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10\u0026name=11","status_code":200,"time":"2025-07-05 14:43:32","timestamp":"2025-07-05 14:43:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":3296300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10\u0026name=%E6%B5%8B%E8%AF%95","status_code":200,"time":"2025-07-05 14:43:37","timestamp":"2025-07-05 14:43:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":19568700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10\u0026name=%E6%B5%8B%E8%AF%95\u0026status=0","status_code":200,"time":"2025-07-05 14:43:40","timestamp":"2025-07-05 14:43:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":15448100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:48","timestamp":"2025-07-05 14:43:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除分类成功: 测试","time":"2025-07-05 14:43:51"}
{"body_size":33,"client_ip":"::1","error":"","latency":60778100,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/categories/1","status_code":200,"time":"2025-07-05 14:43:51","timestamp":"2025-07-05 14:43:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":32279400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 14:43:51","timestamp":"2025-07-05 14:43:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1883300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:43:55","timestamp":"2025-07-05 14:43:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1964000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:43:59","timestamp":"2025-07-05 14:43:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":2891400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:44:12","timestamp":"2025-07-05 14:44:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":5330200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:44:13","timestamp":"2025-07-05 14:44:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":2940200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 14:45:11","timestamp":"2025-07-05 14:45:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:51:59"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:52:50"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 14:53:18"}
{"body_size":-1,"client_ip":"127.0.0.1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-05 17:36:15","timestamp":"2025-07-05 17:36:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"127.0.0.1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/favicon.ico","status_code":404,"time":"2025-07-05 17:36:16","timestamp":"2025-07-05 17:36:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 18:14:06"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 18:14:29"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 18:15:41"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-05 18:16:05"}
{"body_size":91,"client_ip":"::1","error":"","latency":6028600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:22:16","timestamp":"2025-07-05 18:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":3892500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:22:16","timestamp":"2025-07-05 18:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1062300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:23:12","timestamp":"2025-07-05 18:23:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":14927100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:23:12","timestamp":"2025-07-05 18:23:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":13624800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:32:50","timestamp":"2025-07-05 18:32:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":8630700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:32:50","timestamp":"2025-07-05 18:32:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取分类总数失败: invalid connection","time":"2025-07-05 18:34:52"}
{"body_size":33,"client_ip":"::1","error":"","latency":1999000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:34:52","timestamp":"2025-07-05 18:34:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询分类列表失败: invalid connection","time":"2025-07-05 18:34:52"}
{"body_size":33,"client_ip":"::1","error":"","latency":6057900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:34:52","timestamp":"2025-07-05 18:34:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin","time":"2025-07-05 18:35:07"}
{"body_size":533,"client_ip":"::1","error":"","latency":39986600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-05 18:35:07","timestamp":"2025-07-05 18:35:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":77938900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 18:35:08","timestamp":"2025-07-05 18:35:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":17744300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:35:13","timestamp":"2025-07-05 18:35:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":21128200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:35:13","timestamp":"2025-07-05 18:35:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":15607200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:35:30","timestamp":"2025-07-05 18:35:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":18169500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:35:30","timestamp":"2025-07-05 18:35:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":179982800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 18:35:41","timestamp":"2025-07-05 18:35:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":768539300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:35:48","timestamp":"2025-07-05 18:35:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":616041600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:35:48","timestamp":"2025-07-05 18:35:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":58,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10","status_code":401,"time":"2025-07-05 18:37:02","timestamp":"2025-07-05 18:37:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/favicon.ico","status_code":404,"time":"2025-07-05 18:37:02","timestamp":"2025-07-05 18:37:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":288198700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:37:29","timestamp":"2025-07-05 18:37:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":739778100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:37:30","timestamp":"2025-07-05 18:37:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":214009200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:37:45","timestamp":"2025-07-05 18:37:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":101869300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:37:45","timestamp":"2025-07-05 18:37:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":8262300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:37:56","timestamp":"2025-07-05 18:37:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":41304400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:38:45","timestamp":"2025-07-05 18:38:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":153219400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:38:45","timestamp":"2025-07-05 18:38:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":63177300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:38:48","timestamp":"2025-07-05 18:38:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":80723200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:38:48","timestamp":"2025-07-05 18:38:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询视频总数失败: invalid connection","time":"2025-07-05 18:42:34"}
{"body_size":33,"client_ip":"::1","error":"","latency":96258900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:42:34","timestamp":"2025-07-05 18:42:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":150407400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:42:34","timestamp":"2025-07-05 18:42:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"***********","error":"","latency":517500,"level":"info","method":"GET","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-05 18:43:16","timestamp":"2025-07-05 18:43:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":76,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":401,"time":"2025-07-05 18:50:05","timestamp":"2025-07-05 18:50:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":76,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":401,"time":"2025-07-05 18:50:05","timestamp":"2025-07-05 18:50:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"管理员登录成功: admin","time":"2025-07-05 18:50:12"}
{"body_size":533,"client_ip":"::1","error":"","latency":69796100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/public/admin/login","status_code":200,"time":"2025-07-05 18:50:12","timestamp":"2025-07-05 18:50:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":17345000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-05 18:50:12","timestamp":"2025-07-05 18:50:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":13860000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:50:14","timestamp":"2025-07-05 18:50:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":14386400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:50:14","timestamp":"2025-07-05 18:50:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":213734900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:55:48","timestamp":"2025-07-05 18:55:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":950344400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:55:49","timestamp":"2025-07-05 18:55:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":3482200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:56:26","timestamp":"2025-07-05 18:56:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":3997000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:56:26","timestamp":"2025-07-05 18:56:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":1726200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:56:43","timestamp":"2025-07-05 18:56:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":7031900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:56:43","timestamp":"2025-07-05 18:56:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":188488600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:57:03","timestamp":"2025-07-05 18:57:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":197101300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:03","timestamp":"2025-07-05 18:57:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":116320000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:57:05","timestamp":"2025-07-05 18:57:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":127429000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:05","timestamp":"2025-07-05 18:57:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":88331900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 18:57:07","timestamp":"2025-07-05 18:57:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":152845900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:07","timestamp":"2025-07-05 18:57:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":15518600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:08","timestamp":"2025-07-05 18:57:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":32672400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:57:08","timestamp":"2025-07-05 18:57:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":566067200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:15","timestamp":"2025-07-05 18:57:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":984728500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:57:16","timestamp":"2025-07-05 18:57:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":649193600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:57:49","timestamp":"2025-07-05 18:57:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":834707100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:57:50","timestamp":"2025-07-05 18:57:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":4439500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 18:58:17","timestamp":"2025-07-05 18:58:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":14965500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 18:58:17","timestamp":"2025-07-05 18:58:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询视频总数失败: invalid connection","time":"2025-07-05 19:00:20"}
{"body_size":33,"client_ip":"::1","error":"","latency":188287700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 19:00:20","timestamp":"2025-07-05 19:00:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询分类列表失败: invalid connection","time":"2025-07-05 19:00:21"}
{"body_size":33,"client_ip":"::1","error":"","latency":369708200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:00:21","timestamp":"2025-07-05 19:00:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":349036800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:01:05","timestamp":"2025-07-05 19:01:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":405071400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 19:01:05","timestamp":"2025-07-05 19:01:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":32633500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 19:02:33","timestamp":"2025-07-05 19:02:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":38739100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:02:33","timestamp":"2025-07-05 19:02:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":20082000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:02:38","timestamp":"2025-07-05 19:02:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建分类成功: 测试","time":"2025-07-05 19:02:43"}
{"body_size":209,"client_ip":"::1","error":"","latency":28038700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/categories","status_code":200,"time":"2025-07-05 19:02:43","timestamp":"2025-07-05 19:02:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":11088700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 19:02:43","timestamp":"2025-07-05 19:02:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":35131400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:02:46","timestamp":"2025-07-05 19:02:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":53664300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 19:02:46","timestamp":"2025-07-05 19:02:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":34498900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:02:48","timestamp":"2025-07-05 19:02:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":51925300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-05 19:02:48","timestamp":"2025-07-05 19:02:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":34986000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 19:02:49","timestamp":"2025-07-05 19:02:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":61390900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-05 19:02:49","timestamp":"2025-07-05 19:02:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":280,"client_ip":"::1","error":"","latency":1001700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-05 19:02:59","timestamp":"2025-07-05 19:02:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询视频总数失败: invalid connection","time":"2025-07-05 19:06:11"}
{"body_size":33,"client_ip":"::1","error":"","latency":11000500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-05 19:06:11","timestamp":"2025-07-05 19:06:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-06 11:34:04"}
{"body_size":-1,"client_ip":"***********","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 11:36:12","timestamp":"2025-07-06 11:36:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"***********","error":"","latency":1000200,"level":"info","method":"GET","msg":"HTTP Request","path":"/favicon.ico","status_code":404,"time":"2025-07-06 11:36:13","timestamp":"2025-07-06 11:36:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":47965900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-06 11:55:27","timestamp":"2025-07-06 11:55:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":41261300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 11:55:29","timestamp":"2025-07-06 11:55:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":68585500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 11:55:29","timestamp":"2025-07-06 11:55:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":28778100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 13:30:52","timestamp":"2025-07-06 13:30:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":95181900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:52","timestamp":"2025-07-06 13:30:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":1311100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:53","timestamp":"2025-07-06 13:30:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":3730500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:55","timestamp":"2025-07-06 13:30:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":10464500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:55","timestamp":"2025-07-06 13:30:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":2143800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:56","timestamp":"2025-07-06 13:30:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":31512700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:58","timestamp":"2025-07-06 13:30:58","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":15322100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:30:58","timestamp":"2025-07-06 13:30:58","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":14973500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:39:32","timestamp":"2025-07-06 13:39:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":4236000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 13:39:32","timestamp":"2025-07-06 13:39:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":280,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 13:39:53","timestamp":"2025-07-06 13:39:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"************","error":"","latency":568400,"level":"info","method":"POST","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 13:40:55","timestamp":"2025-07-06 13:40:55","user_agent":"Go-http-client/1.1"}
{"body_size":100,"client_ip":"::1","error":"","latency":8895000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 13:43:48","timestamp":"2025-07-06 13:43:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":2534600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 13:43:48","timestamp":"2025-07-06 13:43:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":276,"client_ip":"::1","error":"","latency":1000700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 13:44:12","timestamp":"2025-07-06 13:44:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"***********","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 13:45:24","timestamp":"2025-07-06 13:45:24","user_agent":"Go-http-client/1.1"}
{"body_size":206,"client_ip":"::1","error":"","latency":47033600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:03:23","timestamp":"2025-07-06 14:03:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":284415100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:03:23","timestamp":"2025-07-06 14:03:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":276,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 14:03:35","timestamp":"2025-07-06 14:03:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"************","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 14:04:35","timestamp":"2025-07-06 14:04:35","user_agent":"Go-http-client/1.1"}
{"body_size":100,"client_ip":"::1","error":"","latency":6655500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:18:01","timestamp":"2025-07-06 14:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":2843500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:18:01","timestamp":"2025-07-06 14:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":1384500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:19:46","timestamp":"2025-07-06 14:19:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":4486400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:19:46","timestamp":"2025-07-06 14:19:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":5186400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:20:10","timestamp":"2025-07-06 14:20:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":48842800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:20:10","timestamp":"2025-07-06 14:20:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":7292400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:21:43","timestamp":"2025-07-06 14:21:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":900300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:21:43","timestamp":"2025-07-06 14:21:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":4002500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:22:09","timestamp":"2025-07-06 14:22:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":3873000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:22:09","timestamp":"2025-07-06 14:22:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":25217400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:23:30","timestamp":"2025-07-06 14:23:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":111884500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:23:30","timestamp":"2025-07-06 14:23:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询视频总数失败: invalid connection","time":"2025-07-06 14:26:41"}
{"body_size":33,"client_ip":"::1","error":"","latency":5224200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:26:41","timestamp":"2025-07-06 14:26:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":11449500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 14:26:41","timestamp":"2025-07-06 14:26:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":280,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 14:27:26","timestamp":"2025-07-06 14:27:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建视频成功: input","time":"2025-07-06 14:27:38"}
{"body_size":560,"client_ip":"::1","error":"","latency":67275600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/videos","status_code":200,"time":"2025-07-06 14:27:38","timestamp":"2025-07-06 14:27:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":617,"client_ip":"::1","error":"","latency":28561200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 14:27:38","timestamp":"2025-07-06 14:27:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"************","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:28:28","timestamp":"2025-07-06 14:28:28","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"************","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:29:31","timestamp":"2025-07-06 14:29:31","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"*************","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:29:34","timestamp":"2025-07-06 14:29:34","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"************","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:29:37","timestamp":"2025-07-06 14:29:37","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"***********","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:29:40","timestamp":"2025-07-06 14:29:40","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"***********","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:29:42","timestamp":"2025-07-06 14:29:42","user_agent":"Go-http-client/1.1"}
{"body_size":-1,"client_ip":"***********","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":404,"time":"2025-07-06 14:30:38","timestamp":"2025-07-06 14:30:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
